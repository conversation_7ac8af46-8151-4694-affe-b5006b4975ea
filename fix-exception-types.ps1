# PowerShell script to fix exception types in test methods containing "ShouldThrowArgumentException"
Write-Host "Fixing exception types in test methods containing 'ShouldThrowArgumentException'..."

# List of files that contain "ShouldThrowArgumentException"
$testFiles = @(
    "test/Q.FilterBuilder.Core.Tests/RuleTransformers/BasicRuleTransformerTests.cs",
    "test/Q.FilterBuilder.Core.Tests/RuleTransformers/BetweenTransformerBaseTests.cs",
    "test/Q.FilterBuilder.Core.Tests/RuleTransformers/CollectionParameterTransformerTests.cs",
    "test/Q.FilterBuilder.Core.Tests/RuleTransformers/InTransformerBaseTests.cs",
    "test/Q.FilterBuilder.Core.Tests/TypeConversion/TypeConversionServiceTests.cs",
    "test/Q.FilterBuilder.Linq.Tests/RuleTransformers/DateDiffRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/MySqlEdgeCaseTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/BeginsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/BetweenRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/ContainsRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/DateDiffRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/EndsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/InRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/NotBeginsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/NotBetweenRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/NotContainsRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/NotEndsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/NotInRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/PostgreSqlEdgeCaseTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/BeginsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/BetweenRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/ContainsRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/DateDiffRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/EndsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/InRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/NotBeginsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/NotBetweenRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/NotContainsRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/NotEndsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/NotInRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/BeginsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/BetweenRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/ContainsRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/DateDiffRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/EndsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/InRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/NotBeginsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/NotBetweenRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/NotContainsRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/NotEndsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/NotInRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/SqlServerEdgeCaseTests.cs"
)

foreach ($filePath in $testFiles) {
    if (Test-Path $filePath) {
        Write-Host "Processing: $filePath"
        
        $content = Get-Content $filePath -Raw
        $originalContent = $content
        
        # Find test methods that contain "ShouldThrowArgumentException" and update them
        # Pattern: method name contains "ShouldThrowArgumentException" and uses Assert.Throws<ArgumentNullException>
        $content = $content -replace '(public void \w*ShouldThrowArgumentException[^{]*\{[^}]*Assert\.Throws<)ArgumentNullException(>\(\))', '$1ArgumentException$2'
        
        # Also handle cases where the Assert.Throws is on multiple lines
        $content = $content -replace '(public void \w*ShouldThrowArgumentException[^{]*\{[^}]*Assert\.Throws<)ArgumentNullException(>\([^)]*\))', '$1ArgumentException$2'
        
        # Handle lambda expressions
        $content = $content -replace '(public void \w*ShouldThrowArgumentException[^{]*\{[^}]*Assert\.Throws<)ArgumentNullException(>\([^}]*\}[^}]*\})', '$1ArgumentException$2'
        
        # Only write if content changed
        if ($content -ne $originalContent) {
            Set-Content -Path $filePath -Value $content -NoNewline
            Write-Host "  Updated: $filePath"
        } else {
            Write-Host "  No changes: $filePath"
        }
    } else {
        Write-Host "  File not found: $filePath"
    }
}

Write-Host "Done!"
