# PowerShell script to fix exception types in test methods containing "ShouldThrowArgumentException"
Write-Host "Fixing exception types in test methods containing 'ShouldThrowArgumentException'..."

# List of files that contain "ShouldThrowArgumentException"
$testFiles = @(
    "test/Q.FilterBuilder.Core.Tests/RuleTransformers/BasicRuleTransformerTests.cs",
    "test/Q.FilterBuilder.Core.Tests/RuleTransformers/BetweenTransformerBaseTests.cs",
    "test/Q.FilterBuilder.Core.Tests/RuleTransformers/CollectionParameterTransformerTests.cs",
    "test/Q.FilterBuilder.Core.Tests/RuleTransformers/InTransformerBaseTests.cs",
    "test/Q.FilterBuilder.Core.Tests/TypeConversion/TypeConversionServiceTests.cs",
    "test/Q.FilterBuilder.Linq.Tests/RuleTransformers/DateDiffRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/MySqlEdgeCaseTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/BeginsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/BetweenRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/ContainsRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/DateDiffRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/EndsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/InRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/NotBeginsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/NotBetweenRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/NotContainsRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/NotEndsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.MySql.Tests/RuleTransformers/NotInRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/PostgreSqlEdgeCaseTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/BeginsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/BetweenRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/ContainsRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/DateDiffRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/EndsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/InRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/NotBeginsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/NotBetweenRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/NotContainsRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/NotEndsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.PostgreSql.Tests/RuleTransformers/NotInRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/BeginsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/BetweenRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/ContainsRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/DateDiffRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/EndsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/InRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/NotBeginsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/NotBetweenRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/NotContainsRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/NotEndsWithRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/RuleTransformers/NotInRuleTransformerTests.cs",
    "test/Q.FilterBuilder.SqlServer.Tests/SqlServerEdgeCaseTests.cs"
)

foreach ($filePath in $testFiles) {
    if (Test-Path $filePath) {
        Write-Host "Processing: $filePath"
        
        $content = Get-Content $filePath -Raw
        $originalContent = $content
        
        # Read the file line by line to process it more precisely
        $lines = Get-Content $filePath
        $modified = $false
        
        for ($i = 0; $i -lt $lines.Length; $i++) {
            $line = $lines[$i]
            
            # Check if this line contains a method name with "ShouldThrowArgumentException"
            if ($line -match "public void.*ShouldThrowArgumentException") {
                # Look ahead to find the Assert.Throws line within this method
                for ($j = $i + 1; $j -lt $lines.Length -and $j -lt $i + 20; $j++) {
                    if ($lines[$j] -match "Assert\.Throws<ArgumentNullException>") {
                        $lines[$j] = $lines[$j] -replace "Assert\.Throws<ArgumentNullException>", "Assert.Throws<ArgumentException>"
                        $modified = $true
                        Write-Host "  Updated line $($j + 1): $($lines[$j].Trim())"
                        break
                    }
                    # Stop if we hit another method or end of current method
                    if ($lines[$j] -match "^\s*\[Fact\]" -or $lines[$j] -match "^\s*public ") {
                        break
                    }
                }
            }
        }
        
        # Write the file back if modified
        if ($modified) {
            Set-Content -Path $filePath -Value $lines
            Write-Host "  File updated: $filePath"
        } else {
            Write-Host "  No changes: $filePath"
        }
    } else {
        Write-Host "  File not found: $filePath"
    }
}

Write-Host "Done!"
