# PowerShell script to fix remaining exception types in test methods
Write-Host "Fixing remaining exception types in test methods..."

# Get all test files that might have remaining ArgumentNullException issues
$testFiles = Get-ChildItem -Path "test" -Recurse -Filter "*.cs" | Where-Object { 
    $_.FullName -like "*Tests.cs" -and $_.FullName -notlike "*TestHelpers*" -and $_.FullName -notlike "*\obj\*" -and $_.FullName -notlike "*\bin\*"
}

foreach ($file in $testFiles) {
    Write-Host "Processing: $($file.FullName)"
    
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Fix specific patterns for extension method tests that should expect ArgumentException
    if ($file.Name -like "*ServiceCollectionExtensionsTests.cs" -or $file.Name -like "*FilterBuilderOptionsTests.cs") {
        # These tests are checking for null parameters in extension methods, should expect ArgumentException
        $content = $content -replace 'Assert\.Throws<ArgumentNullException>\(\(\) => [^}]*\.Add\w*FilterBuilder\([^)]*null[^)]*\)\)', 'Assert.Throws<ArgumentException>(() => $1)'
        $content = $content -replace 'Assert\.Throws<ArgumentNullException>\(\(\) => [^}]*\.Configure\w*\([^)]*null[^)]*\)\)', 'Assert.Throws<ArgumentException>(() => $1)'
    }
    
    # Fix edge case tests that should expect ArgumentException for validation errors
    if ($file.Name -like "*EdgeCaseTests.cs") {
        # Field name validation should expect ArgumentException
        $content = $content -replace '(WithNullFieldName.*ShouldThrowArgumentNullException[^}]*Assert\.Throws<)ArgumentNullException(>\(\))', '$1ArgumentException$2'
        $content = $content -replace '(WithEmptyFieldName.*ShouldThrowArgumentNullException[^}]*Assert\.Throws<)ArgumentNullException(>\(\))', '$1ArgumentException$2'
        $content = $content -replace '(WithWhitespaceFieldName.*ShouldThrowArgumentNullException[^}]*Assert\.Throws<)ArgumentNullException(>\(\))', '$1ArgumentException$2'
    }
    
    # Only write if content changed
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  Updated: $($file.FullName)"
    } else {
        Write-Host "  No changes: $($file.FullName)"
    }
}

Write-Host "Done!"
