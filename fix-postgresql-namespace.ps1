# PowerShell script to fix PostgreSQL namespace issues
param(
    [string]$TestDirectory = "test/Q.FilterBuilder.PostgreSql.Tests"
)

Write-Host "Fixing PostgreSQL namespace issues..."

# Get all C# test files in PostgreSQL tests
$testFiles = Get-ChildItem -Path $TestDirectory -Recurse -Filter "*.cs"

foreach ($file in $testFiles) {
    Write-Host "Processing: $($file.FullName)"
    
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Fix the namespace from PostgreSQL to PostgreSql
    $content = $content -replace "using Q\.FilterBuilder\.PostgreSQL;", "using Q.FilterBuilder.PostgreSql;"
    
    # Only write if content changed
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  Updated: $($file.Name)"
    } else {
        Write-Host "  No changes: $($file.Name)"
    }
}

Write-Host "Done!"
