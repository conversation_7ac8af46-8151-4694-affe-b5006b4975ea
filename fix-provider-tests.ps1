# PowerShell script to fix provider test files to use real format providers
param(
    [string]$TestDirectory = "test"
)

Write-Host "Fixing provider test files to use real format providers..."

# Define provider mappings
$providers = @{
    "SqlServer" = @{
        "Namespace" = "Q.FilterBuilder.SqlServer"
        "FormatProvider" = "SqlServerFormatProvider"
    }
    "MySQL" = @{
        "Namespace" = "Q.FilterBuilder.MySQL"
        "FormatProvider" = "MySqlFormatProvider"
    }
    "PostgreSQL" = @{
        "Namespace" = "Q.FilterBuilder.PostgreSql"
        "FormatProvider" = "PostgreSqlFormatProvider"
    }
}

foreach ($providerName in $providers.Keys) {
    $provider = $providers[$providerName]
    $testPath = Join-Path $TestDirectory "Q.FilterBuilder.$providerName.Tests"
    
    if (Test-Path $testPath) {
        Write-Host "Processing $providerName tests..."
        
        # Get all C# test files for this provider
        $testFiles = Get-ChildItem -Path $testPath -Recurse -Filter "*.cs"
        
        foreach ($file in $testFiles) {
            Write-Host "  Processing: $($file.Name)"
            
            $content = Get-Content $file.FullName -Raw
            $originalContent = $content
            
            # Add using statement for the provider namespace if not present
            if ($content -notmatch "using $($provider.Namespace);") {
                $content = $content -replace "(using Q\.FilterBuilder\.Core\.Models;)", "using $($provider.Namespace);`n`$1"
            }
            
            # Replace GetTestFormatProvider() with new [Provider]FormatProvider()
            $content = $content -replace "GetTestFormatProvider\(\)", "new $($provider.FormatProvider)()"
            
            # Replace new TestFormatProvider() with new [Provider]FormatProvider()
            $content = $content -replace "new TestFormatProvider\(\)", "new $($provider.FormatProvider)()"
            
            # Remove TestFormatProvider class definitions
            $content = $content -replace "private class TestFormatProvider : IQueryFormatProvider\s*\{[^}]*\}", ""
            
            # Remove TestHelpers using statements
            $content = $content -replace "using Q\.FilterBuilder\.TestHelpers;\s*\n", ""
            
            # Remove inheritance from BaseRuleTransformerTest
            $content = $content -replace " : BaseRuleTransformerTest", ""
            
            # Clean up extra whitespace
            $content = $content -replace "\n\s*\n\s*\n", "`n`n"
            
            # Only write if content changed
            if ($content -ne $originalContent) {
                Set-Content -Path $file.FullName -Value $content -NoNewline
                Write-Host "    Updated: $($file.Name)"
            } else {
                Write-Host "    No changes: $($file.Name)"
            }
        }
    } else {
        Write-Host "Skipping $providerName - directory not found"
    }
}

Write-Host "Done!"
