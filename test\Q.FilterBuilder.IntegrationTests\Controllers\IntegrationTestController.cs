using Microsoft.AspNetCore.Mvc;
using Q.FilterBuilder.Core;
using Q.FilterBuilder.Core.Extensions;
using Q.FilterBuilder.Core.Models;
using Q.FilterBuilder.IntegrationTests.Configuration;
using Q.FilterBuilder.IntegrationTests.Services;

namespace Q.FilterBuilder.IntegrationTests.Controllers;

/// <summary>
/// controller for integration testing of FilterBuilder functionality
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class IntegrationTestController : ControllerBase
{
    private readonly IFilterBuilder _filterBuilder;
    private readonly IOrmExecutionService _ormService;
    private readonly TestConfiguration _testConfig;

    public IntegrationTestController(
        IFilterBuilder filterBuilder,
        IOrmExecutionService ormService,
        TestConfiguration testConfig)
    {
        _filterBuilder = filterBuilder;
        _ormService = ormService;
        _testConfig = testConfig;
    }

    /// <summary>
    /// Execute filter using Entity Framework Core
    /// </summary>
    [HttpPost("execute-efcore-users")]
    public async Task<IActionResult> ExecuteEFCoreUsers([FromBody] FilterGroup filterGroup)
    {
        try
        {
            // Use the new EF Core extension method
            var efCoreResult = _filterBuilder.BuildForEfCore(filterGroup);
            var results = await _ormService.ExecuteWithEntityFrameworkAsync(efCoreResult.FormattedQuery, efCoreResult.Parameters);

            return Ok(new
            {
                WhereClause = efCoreResult.WhereClause,
                FormattedQuery = efCoreResult.FormattedQuery,
                Parameters = efCoreResult.Parameters,
                Results = results,
                results.Count,
                ORM = "EntityFramework"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, ex.StackTrace });
        }
    }

    /// <summary>
    /// Execute filter using Dapper
    /// </summary>
    [HttpPost("execute-dapper-users")]
    public async Task<IActionResult> ExecuteDapperUsers([FromBody] FilterGroup filterGroup)
    {
        try
        {
            // Use the new Dapper extension method
            var dapperResult = _filterBuilder.BuildForDapper(filterGroup);
            var results = await _ormService.ExecuteWithDapperAsync(dapperResult.WhereClause, dapperResult.Parameters);

            return Ok(new
            {
                WhereClause = dapperResult.WhereClause,
                Parameters = dapperResult.Parameters,
                Results = results,
                results.Count,
                ORM = "Dapper"
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, ex.StackTrace });
        }
    }

    /// <summary>
    /// Execute filter using ADO.NET
    /// </summary>
    //[HttpPost("execute-adonet-users")]
    //public async Task<IActionResult> ExecuteAdoNetUsers([FromBody] FilterGroup filterGroup)
    //{
    //    try
    //    {
    //        // Use the new ADO.NET extension method
    //        var adoNetResult = _filterBuilder.BuildForAdoNet(filterGroup);
    //        var results = await _ormService.ExecuteWithAdoNetAsync(adoNetResult.WhereClause, adoNetResult.ParameterValues);

    //        return Ok(new
    //        {
    //            WhereClause = adoNetResult.WhereClause,
    //            Parameters = adoNetResult.ParameterValues,
    //            Results = results,
    //            results.Count,
    //            ORM = "ADO.NET"
    //        });
    //    }
    //    catch (Exception ex)
    //    {
    //        return BadRequest(new { Error = ex.Message, ex.StackTrace });
    //    }
    //}

    /// <summary>
    /// Execute filter for general users table operations
    /// </summary>
    [HttpPost("execute-users-filter")]
    public async Task<IActionResult> ExecuteUsersFilter([FromBody] FilterGroup filterGroup)
    {
        try
        {
            // Use the new Dapper extension method
            var dapperResult = _filterBuilder.BuildForDapper(filterGroup);

            var results = await _ormService.ExecuteWithDapperAsync(dapperResult.WhereClause, dapperResult.Parameters);

            return Ok(new
            {
                WhereClause = dapperResult.WhereClause,
                Parameters = dapperResult.Parameters,
                Results = results,
                results.Count
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new {
                Error = ex.Message,
                ex.StackTrace,
                ExceptionType = ex.GetType().Name,
                InnerException = ex.InnerException?.Message
            });
        }
    }

    /// <summary>
    /// Execute filter and return results (generic endpoint)
    /// </summary>
    [HttpPost("execute-filter")]
    public async Task<IActionResult> ExecuteFilter([FromBody] FilterGroup filterGroup)
    {
        try
        {
            // Use the new Dapper extension method
            var dapperResult = _filterBuilder.BuildForDapper(filterGroup);
            var results = await _ormService.ExecuteWithDapperAsync(dapperResult.WhereClause, dapperResult.Parameters);

            return Ok(new
            {
                WhereClause = dapperResult.WhereClause,
                Parameters = dapperResult.Parameters,
                Results = results,
                results.Count
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { Error = ex.Message, ex.StackTrace });
        }
    }

    /// <summary>
    /// Build query and return query string and parameters without execution
    /// </summary>
    [HttpPost("build-query")]
    public IActionResult BuildQuery([FromBody] FilterGroup filterGroup)
    {
        try
        {
            if (filterGroup == null)
            {
                return BadRequest(new { Error = "FilterGroup is null", Details = "Request body could not be deserialized to FilterGroup" });
            }

            var (query, parameters) = _filterBuilder.Build(filterGroup);

            return Ok(new
            {
                Query = query,
                WhereClause = query,
                Parameters = parameters
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new {
                Error = ex.Message,
                ex.StackTrace,
                ExceptionType = ex.GetType().Name,
                InnerException = ex.InnerException?.Message
            });
        }
    }
}
