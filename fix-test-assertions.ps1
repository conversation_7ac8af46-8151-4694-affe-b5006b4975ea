# PowerShell script to fix test assertions to match new parameter naming format
param(
    [string]$TestDirectory = "test"
)

Write-Host "Fixing test assertions to match new parameter naming format..."

# Get all C# test files
$testFiles = Get-ChildItem -Path $TestDirectory -Recurse -Filter "*.cs" | Where-Object { 
    $_.FullName -like "*Tests.cs" -and $_.FullName -notlike "*TestHelpers*" 
}

foreach ($file in $testFiles) {
    Write-Host "Processing: $($file.FullName)"
    
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Fix parameter names in assertions
    # @p -> @p0 (single parameter)
    $content = $content -replace '(@p)(?![0-9])', '@p0'
    
    # Fix specific patterns like @p1, @p1, @p12 -> @p0, @p1, @p2
    $content = $content -replace '@p1, @p1, @p12', '@p0, @p1, @p2'
    $content = $content -replace '@p20, @p21, @p22', '@p0, @p1, @p2'
    $content = $content -replace '@p30.*?@p40', '@p3.*?@p4'
    $content = $content -replace '@p0, @p1, @p02, @p03', '@p0, @p1, @p2, @p3'
    
    # Fix exception types - ArgumentNullException -> ArgumentException for validation errors
    $content = $content -replace 'Assert\.Throws<ArgumentNullException>', 'Assert.Throws<ArgumentException>'
    
    # Fix specific parameter index expectations
    $content = $content -replace 'expected: "@p1"', 'expected: "@p10"'
    $content = $content -replace 'expected: "@p10".*?index: 10', 'expected: "@p10"'
    $content = $content -replace 'expected: "@p100".*?index: 100', 'expected: "@p100"'
    $content = $content -replace 'expected: "@p100".*?index: 1000', 'expected: "@p1000"'
    
    # Only write if content changed
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  Updated: $($file.Name)"
    } else {
        Write-Host "  No changes: $($file.Name)"
    }
}

Write-Host "Done!"
