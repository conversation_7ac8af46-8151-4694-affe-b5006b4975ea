# PowerShell script to fix MySQL namespace issues
Write-Host "Fixing MySQL namespace issues..."

# Get all C# files in MySQL tests
$testFiles = Get-ChildItem -Path "test/Q.FilterBuilder.MySql.Tests" -Recurse -Filter "*.cs" | Where-Object { 
    $_.FullName -notlike "*\obj\*" -and $_.FullName -notlike "*\bin\*"
}

foreach ($file in $testFiles) {
    Write-Host "Processing: $($file.FullName)"
    
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Fix the namespace from MySQL to MySql
    $content = $content -replace "Q\.FilterBuilder\.MySQL", "Q.FilterBuilder.MySql"
    
    # Only write if content changed
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  Updated: $($file.Name)"
    } else {
        Write-Host "  No changes: $($file.Name)"
    }
}

Write-Host "Done!"
