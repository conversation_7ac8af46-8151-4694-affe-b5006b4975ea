{"Version": 1, "WorkspaceRootPath": "C:\\git-repos\\DynamicWhereBuilder\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{C3D4E5F6-A7B8-9012-CDEF-345678901234}|test\\Q.FilterBuilder.Linq.Tests\\Q.FilterBuilder.Linq.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.linq.tests\\extensions\\linqfilterbuilderoptionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C3D4E5F6-A7B8-9012-CDEF-345678901234}|test\\Q.FilterBuilder.Linq.Tests\\Q.FilterBuilder.Linq.Tests.csproj|solutionrelative:test\\q.filterbuilder.linq.tests\\extensions\\linqfilterbuilderoptionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|test\\Q.FilterBuilder.MySql.Tests\\Q.FilterBuilder.MySql.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.mysql.tests\\extensions\\mysqlfilterbuilderoptionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}|test\\Q.FilterBuilder.MySql.Tests\\Q.FilterBuilder.MySql.Tests.csproj|solutionrelative:test\\q.filterbuilder.mysql.tests\\extensions\\mysqlfilterbuilderoptionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B2C3D4E5-F6A7-8901-BCDE-F23456789012}|test\\Q.FilterBuilder.PostgreSql.Tests\\Q.FilterBuilder.PostgreSql.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.postgresql.tests\\extensions\\postgresqlfilterbuilderoptionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B2C3D4E5-F6A7-8901-BCDE-F23456789012}|test\\Q.FilterBuilder.PostgreSql.Tests\\Q.FilterBuilder.PostgreSql.Tests.csproj|solutionrelative:test\\q.filterbuilder.postgresql.tests\\extensions\\postgresqlfilterbuilderoptionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B2C3D4E5-F6A7-8901-BCDE-F23456789012}|test\\Q.FilterBuilder.PostgreSql.Tests\\Q.FilterBuilder.PostgreSql.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.postgresql.tests\\postgresqlprovidertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B2C3D4E5-F6A7-8901-BCDE-F23456789012}|test\\Q.FilterBuilder.PostgreSql.Tests\\Q.FilterBuilder.PostgreSql.Tests.csproj|solutionrelative:test\\q.filterbuilder.postgresql.tests\\postgresqlprovidertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.sqlserver.tests\\ruletransformers\\notinruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|solutionrelative:test\\q.filterbuilder.sqlserver.tests\\ruletransformers\\notinruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.sqlserver.tests\\ruletransformers\\notendswithruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|solutionrelative:test\\q.filterbuilder.sqlserver.tests\\ruletransformers\\notendswithruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.sqlserver.tests\\ruletransformers\\inruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|solutionrelative:test\\q.filterbuilder.sqlserver.tests\\ruletransformers\\inruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.sqlserver.tests\\ruletransformers\\endswithruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|solutionrelative:test\\q.filterbuilder.sqlserver.tests\\ruletransformers\\endswithruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.sqlserver.tests\\ruletransformers\\datediffruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|solutionrelative:test\\q.filterbuilder.sqlserver.tests\\ruletransformers\\datediffruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.sqlserver.tests\\extensions\\sqlserverfilterbuilderoptionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|solutionrelative:test\\q.filterbuilder.sqlserver.tests\\extensions\\sqlserverfilterbuilderoptionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.sqlserver.tests\\sqlserverintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|solutionrelative:test\\q.filterbuilder.sqlserver.tests\\sqlserverintegrationtests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.sqlserver.tests\\sqlserveredgecasetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{F8A2E1D4-9B3C-4E5F-8A7B-1C2D3E4F5A6B}|test\\Q.FilterBuilder.SqlServer.Tests\\Q.FilterBuilder.SqlServer.Tests.csproj|solutionrelative:test\\q.filterbuilder.sqlserver.tests\\sqlserveredgecasetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{83B36E5C-6E79-426C-9675-DF2C2B53BC85}|test\\Q.FilterBuilder.Core.Tests\\Q.FilterBuilder.Core.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.core.tests\\extensions\\filterbuilderservicecollectionextensionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{83B36E5C-6E79-426C-9675-DF2C2B53BC85}|test\\Q.FilterBuilder.Core.Tests\\Q.FilterBuilder.Core.Tests.csproj|solutionrelative:test\\q.filterbuilder.core.tests\\extensions\\filterbuilderservicecollectionextensionstests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{83B36E5C-6E79-426C-9675-DF2C2B53BC85}|test\\Q.FilterBuilder.Core.Tests\\Q.FilterBuilder.Core.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.core.tests\\ruletransformers\\baseruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{83B36E5C-6E79-426C-9675-DF2C2B53BC85}|test\\Q.FilterBuilder.Core.Tests\\Q.FilterBuilder.Core.Tests.csproj|solutionrelative:test\\q.filterbuilder.core.tests\\ruletransformers\\baseruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{83B36E5C-6E79-426C-9675-DF2C2B53BC85}|test\\Q.FilterBuilder.Core.Tests\\Q.FilterBuilder.Core.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.core.tests\\ruletransformers\\betweentransformerbasetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{83B36E5C-6E79-426C-9675-DF2C2B53BC85}|test\\Q.FilterBuilder.Core.Tests\\Q.FilterBuilder.Core.Tests.csproj|solutionrelative:test\\q.filterbuilder.core.tests\\ruletransformers\\betweentransformerbasetests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{BC7836FD-0BA8-4E53-87C4-E465C695303E}|src\\Q.FilterBuilder.SqlServer\\Q.FilterBuilder.SqlServer.csproj|c:\\git-repos\\dynamicwherebuilder\\src\\q.filterbuilder.sqlserver\\sqlserverformatprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{BC7836FD-0BA8-4E53-87C4-E465C695303E}|src\\Q.FilterBuilder.SqlServer\\Q.FilterBuilder.SqlServer.csproj|solutionrelative:src\\q.filterbuilder.sqlserver\\sqlserverformatprovider.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{C3D4E5F6-A7B8-9012-CDEF-345678901234}|test\\Q.FilterBuilder.Linq.Tests\\Q.FilterBuilder.Linq.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.linq.tests\\ruletransformers\\datediffruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{C3D4E5F6-A7B8-9012-CDEF-345678901234}|test\\Q.FilterBuilder.Linq.Tests\\Q.FilterBuilder.Linq.Tests.csproj|solutionrelative:test\\q.filterbuilder.linq.tests\\ruletransformers\\datediffruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{B2C3D4E5-F6A7-8901-BCDE-F23456789012}|test\\Q.FilterBuilder.PostgreSql.Tests\\Q.FilterBuilder.PostgreSql.Tests.csproj|c:\\git-repos\\dynamicwherebuilder\\test\\q.filterbuilder.postgresql.tests\\ruletransformers\\betweenruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{B2C3D4E5-F6A7-8901-BCDE-F23456789012}|test\\Q.FilterBuilder.PostgreSql.Tests\\Q.FilterBuilder.PostgreSql.Tests.csproj|solutionrelative:test\\q.filterbuilder.postgresql.tests\\ruletransformers\\betweenruletransformertests.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 581, "SelectedChildIndex": 0, "Children": [{"$type": "Document", "DocumentIndex": 0, "Title": "LinqFilterBuilderOptionsTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.Linq.Tests\\Extensions\\LinqFilterBuilderOptionsTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.Linq.Tests\\Extensions\\LinqFilterBuilderOptionsTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.Linq.Tests\\Extensions\\LinqFilterBuilderOptionsTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.Linq.Tests\\Extensions\\LinqFilterBuilderOptionsTests.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAYwBsAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T07:10:11.851Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "MySqlFilterBuilderOptionsTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.MySql.Tests\\Extensions\\MySqlFilterBuilderOptionsTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.MySql.Tests\\Extensions\\MySqlFilterBuilderOptionsTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.MySql.Tests\\Extensions\\MySqlFilterBuilderOptionsTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.MySql.Tests\\Extensions\\MySqlFilterBuilderOptionsTests.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAYwBsAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T07:09:41.938Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "PostgreSqlFilterBuilderOptionsTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.PostgreSql.Tests\\Extensions\\PostgreSqlFilterBuilderOptionsTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.PostgreSql.Tests\\Extensions\\PostgreSqlFilterBuilderOptionsTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.PostgreSql.Tests\\Extensions\\PostgreSqlFilterBuilderOptionsTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.PostgreSql.Tests\\Extensions\\PostgreSqlFilterBuilderOptionsTests.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAYwBsAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T07:08:12.074Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "PostgreSqlProviderTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.PostgreSql.Tests\\PostgreSqlProviderTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.PostgreSql.Tests\\PostgreSqlProviderTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.PostgreSql.Tests\\PostgreSqlProviderTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.PostgreSql.Tests\\PostgreSqlProviderTests.cs", "ViewState": "AgIAAC0AAAAAAAAAAAAuwDkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T07:07:29.63Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "NotInRuleTransformerTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\NotInRuleTransformerTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\NotInRuleTransformerTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\NotInRuleTransformerTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\NotInRuleTransformerTests.cs", "ViewState": "AgIAABoBAAAAAAAAAAAiwC4BAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T07:05:10.844Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "NotEndsWithRuleTransformerTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\NotEndsWithRuleTransformerTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\NotEndsWithRuleTransformerTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\NotEndsWithRuleTransformerTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\NotEndsWithRuleTransformerTests.cs", "ViewState": "AgIAANYAAAAAAAAAAAAiwO0AAAChAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T07:04:27.563Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "InRuleTransformerTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\InRuleTransformerTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\InRuleTransformerTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\InRuleTransformerTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\InRuleTransformerTests.cs", "ViewState": "AgIAAD4AAAAAAAAAAAAiwEwAAAA9AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T07:03:52.857Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "EndsWithRuleTransformerTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\EndsWithRuleTransformerTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\EndsWithRuleTransformerTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\EndsWithRuleTransformerTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\EndsWithRuleTransformerTests.cs", "ViewState": "AgIAAMoAAAAAAAAAAAAiwOAAAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T07:03:38.229Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "DateDiffRuleTransformerTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\DateDiffRuleTransformerTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\DateDiffRuleTransformerTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\DateDiffRuleTransformerTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.SqlServer.Tests\\RuleTransformers\\DateDiffRuleTransformerTests.cs", "ViewState": "AgIAAIABAAAAAAAAAAAiwJQBAAAuAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T07:03:08.746Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "SqlServerFilterBuilderOptionsTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\Extensions\\SqlServerFilterBuilderOptionsTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.SqlServer.Tests\\Extensions\\SqlServerFilterBuilderOptionsTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\Extensions\\SqlServerFilterBuilderOptionsTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.SqlServer.Tests\\Extensions\\SqlServerFilterBuilderOptionsTests.cs", "ViewState": "AgIAAAkAAAAAAAAAAAAUwBwAAAAyAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T07:02:42.298Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "SqlServerIntegrationTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\SqlServerIntegrationTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.SqlServer.Tests\\SqlServerIntegrationTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\SqlServerIntegrationTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.SqlServer.Tests\\SqlServerIntegrationTests.cs", "ViewState": "AgIAAOkAAAAAAAAAAAAqwPsAAAA0AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T07:01:25.429Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "SqlServerEdgeCaseTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\SqlServerEdgeCaseTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.SqlServer.Tests\\SqlServerEdgeCaseTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.SqlServer.Tests\\SqlServerEdgeCaseTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.SqlServer.Tests\\SqlServerEdgeCaseTests.cs", "ViewState": "AgIAAJ4AAAAAAAAAAAAiwK8AAAA5AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T07:00:57.336Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "FilterBuilderServiceCollectionExtensionsTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.Core.Tests\\Extensions\\FilterBuilderServiceCollectionExtensionsTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.Core.Tests\\Extensions\\FilterBuilderServiceCollectionExtensionsTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.Core.Tests\\Extensions\\FilterBuilderServiceCollectionExtensionsTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.Core.Tests\\Extensions\\FilterBuilderServiceCollectionExtensionsTests.cs", "ViewState": "AgIAAGcAAAAAAAAAAAAAAAUBAABFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T06:59:57.292Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "BaseRuleTransformerTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.Core.Tests\\RuleTransformers\\BaseRuleTransformerTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.Core.Tests\\RuleTransformers\\BaseRuleTransformerTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.Core.Tests\\RuleTransformers\\BaseRuleTransformerTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.Core.Tests\\RuleTransformers\\BaseRuleTransformerTests.cs", "ViewState": "AgIAAKAAAAAAAAAAAAAUwKUAAABEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T06:58:59.344Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "BetweenTransformerBaseTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.Core.Tests\\RuleTransformers\\BetweenTransformerBaseTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.Core.Tests\\RuleTransformers\\BetweenTransformerBaseTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.Core.Tests\\RuleTransformers\\BetweenTransformerBaseTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.Core.Tests\\RuleTransformers\\BetweenTransformerBaseTests.cs", "ViewState": "AgIAAMIAAAAAAAAAAAAiwNMAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T06:57:41.466Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "SqlServerFormatProvider.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.SqlServer\\SqlServerFormatProvider.cs", "RelativeDocumentMoniker": "src\\Q.FilterBuilder.SqlServer\\SqlServerFormatProvider.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\src\\Q.FilterBuilder.SqlServer\\SqlServerFormatProvider.cs", "RelativeToolTip": "src\\Q.FilterBuilder.SqlServer\\SqlServerFormatProvider.cs", "ViewState": "AgIAAA0AAAAAAAAAAAAgwBsAAAAtAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T06:58:16.424Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "BetweenRuleTransformerTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.PostgreSql.Tests\\RuleTransformers\\BetweenRuleTransformerTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.PostgreSql.Tests\\RuleTransformers\\BetweenRuleTransformerTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.PostgreSql.Tests\\RuleTransformers\\BetweenRuleTransformerTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.PostgreSql.Tests\\RuleTransformers\\BetweenRuleTransformerTests.cs", "ViewState": "AgIAABoAAAAAAAAAAAAowAsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T06:38:00.161Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "DateDiffRuleTransformerTests.cs", "DocumentMoniker": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.Linq.Tests\\RuleTransformers\\DateDiffRuleTransformerTests.cs", "RelativeDocumentMoniker": "test\\Q.FilterBuilder.Linq.Tests\\RuleTransformers\\DateDiffRuleTransformerTests.cs", "ToolTip": "C:\\git-repos\\DynamicWhereBuilder\\test\\Q.FilterBuilder.Linq.Tests\\RuleTransformers\\DateDiffRuleTransformerTests.cs", "RelativeToolTip": "test\\Q.FilterBuilder.Linq.Tests\\RuleTransformers\\DateDiffRuleTransformerTests.cs", "ViewState": "AgIAABgAAAAAAAAAAAAqwDIAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-06-16T06:37:35.086Z", "EditorCaption": ""}]}]}]}