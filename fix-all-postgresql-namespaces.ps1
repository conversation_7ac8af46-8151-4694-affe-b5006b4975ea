# PowerShell script to fix all PostgreSQL namespace issues
Write-Host "Fixing all PostgreSQL namespace issues..."

# Get all C# files in PostgreSQL tests
$testFiles = Get-ChildItem -Path "test/Q.FilterBuilder.PostgreSql.Tests" -Recurse -Filter "*.cs" | Where-Object { 
    $_.FullName -notlike "*\obj\*" -and $_.FullName -notlike "*\bin\*"
}

foreach ($file in $testFiles) {
    Write-Host "Processing: $($file.FullName)"
    
    $content = Get-Content $file.FullName -Raw
    $originalContent = $content
    
    # Fix the namespace from PostgreSQL to PostgreSql
    $content = $content -replace "Q\.FilterBuilder\.PostgreSQL", "Q.FilterBuilder.PostgreSql"
    
    # Also fix parameter expectations for PostgreSQL (uses $1, $2, etc.)
    $content = $content -replace '\$10', '$1'
    $content = $content -replace '\$20', '$1'
    $content = $content -replace '\$21', '$2'
    $content = $content -replace '\$22', '$3'
    $content = $content -replace '\$30', '$1'
    $content = $content -replace '\$31', '$2'
    $content = $content -replace '\$1(?![0-9])', '$1'
    
    # Only write if content changed
    if ($content -ne $originalContent) {
        Set-Content -Path $file.FullName -Value $content -NoNewline
        Write-Host "  Updated: $($file.Name)"
    } else {
        Write-Host "  No changes: $($file.Name)"
    }
}

Write-Host "Done!"
